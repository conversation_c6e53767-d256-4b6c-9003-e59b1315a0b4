@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

echo 测试Qt平台插件修复...
echo.

:: 获取脚本所在目录作为项目根目录
set "PROJECT_ROOT=%~dp0"
set "PROJECT_ROOT=%PROJECT_ROOT:~0,-1%"

:: 配置Python解释器路径
set "PYTHON_CMD="
if exist "%PROJECT_ROOT%\python\python.exe" (
    set "PYTHON_CMD=%PROJECT_ROOT%\python\python.exe"
) else if exist "%PROJECT_ROOT%\.venv\Scripts\python.exe" (
    set "PYTHON_CMD=%PROJECT_ROOT%\.venv\Scripts\python.exe"
) else (
    set "PYTHON_CMD=python"
)

:: 激活虚拟环境
if exist "%PROJECT_ROOT%\.venv\Scripts\activate.bat" (
    call "%PROJECT_ROOT%\.venv\Scripts\activate.bat"
)

:: 设置Qt环境变量
set "QT_PLUGIN_PATH=%PROJECT_ROOT%\.venv\Lib\site-packages\PyQt5\Qt5\plugins"
set "QT_QPA_PLATFORM_PLUGIN_PATH=%PROJECT_ROOT%\.venv\Lib\site-packages\PyQt5\Qt5\plugins\platforms"
set "PATH=%PROJECT_ROOT%\.venv\Lib\site-packages\PyQt5\Qt5\bin;%PATH%"

echo 环境变量设置:
echo   QT_PLUGIN_PATH: %QT_PLUGIN_PATH%
echo   QT_QPA_PLATFORM_PLUGIN_PATH: %QT_QPA_PLATFORM_PLUGIN_PATH%
echo.

echo 运行Qt测试脚本...
"%PYTHON_CMD%" test_qt_fix.py

echo.
echo 测试完成
pause
