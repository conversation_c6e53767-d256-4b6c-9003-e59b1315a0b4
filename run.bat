@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

:: ========================================
:: 智能驾驶试验管控工具 - 启动脚本
:: 版本: 5.0
:: 日期: 2025-08-04
:: 功能: 完整的离线环境配置和应用启动
:: ========================================

title 智能驾驶试验管控工具 - 启动器 v5.0

:: 显示启动横幅
echo.
echo ========================================
echo   智能驾驶试验管控工具
echo   启动器 v5.0
echo ========================================
echo 版本: 5.0 (完整离线版)
echo 功能: 自动环境配置、依赖验证、应用启动
echo 平台: Windows 离线环境
echo ========================================
echo.

:: 记录启动时间
set start_time=%time%
echo 启动时间: %start_time%
echo.

:: 获取脚本所在目录作为项目根目录
set "PROJECT_ROOT=%~dp0"
set "PROJECT_ROOT=%PROJECT_ROOT:~0,-1%"
cd /d "%PROJECT_ROOT%"

echo [信息] 项目根目录: %PROJECT_ROOT%
echo.

:: ========================================
:: 第1步: 检查基础环境
:: ========================================
echo [第1步/6] 检查基础环境...
echo 当前目录: %CD%
echo 操作系统: %OS%
echo 计算机名: %COMPUTERNAME%
echo 用户名: %USERNAME%
echo.

:: ========================================
:: 第2步: 配置Python解释器路径
:: ========================================
echo [第2步/6] 配置Python解释器路径...

:: 优先使用项目内置的Python解释器
set "PYTHON_CMD="
if exist "%PROJECT_ROOT%\python\python.exe" (
    set "PYTHON_CMD=%PROJECT_ROOT%\python\python.exe"
    echo [成功] 找到项目内置Python解释器: %PYTHON_CMD%
) else (
    :: 尝试使用虚拟环境中的Python
    if exist "%PROJECT_ROOT%\.venv\Scripts\python.exe" (
        set "PYTHON_CMD=%PROJECT_ROOT%\.venv\Scripts\python.exe"
        echo [成功] 找到虚拟环境Python解释器: %PYTHON_CMD%
    ) else (
        :: 尝试使用系统Python
        where python >nul 2>&1
        if !errorlevel! equ 0 (
            set "PYTHON_CMD=python"
            echo [警告] 使用系统Python解释器
        ) else (
            echo [错误] 未找到可用的Python解释器
            echo [提示] 请确保以下任一条件满足:
            echo   1. 项目根目录存在 python\python.exe
            echo   2. 虚拟环境 .venv\Scripts\python.exe 存在
            echo   3. 系统已安装Python并添加到PATH
            goto :error_exit
        )
    )
)

:: 验证Python版本
echo [信息] 验证Python版本...
"%PYTHON_CMD%" --version
if !errorlevel! neq 0 (
    echo [错误] Python解释器无法正常运行
    goto :error_exit
)
echo.

:: ========================================
:: 第3步: 虚拟环境配置
:: ========================================
echo [第3步/6] 配置虚拟环境...

:: 检查虚拟环境是否存在
if not exist "%PROJECT_ROOT%\.venv" (
    echo [警告] 虚拟环境不存在，正在创建...
    "%PYTHON_CMD%" -m venv "%PROJECT_ROOT%\.venv"
    if !errorlevel! neq 0 (
        echo [错误] 虚拟环境创建失败
        echo [提示] 请检查Python安装是否完整，或手动运行:
        echo   "%PYTHON_CMD%" -m venv .venv
        goto :error_exit
    )
    echo [成功] 虚拟环境创建完成
) else (
    echo [信息] 虚拟环境已存在
)

:: 检查虚拟环境完整性
if not exist "%PROJECT_ROOT%\.venv\Scripts\python.exe" (
    echo [错误] 虚拟环境不完整，缺少Python解释器
    echo [提示] 请删除 .venv 目录后重新运行此脚本
    goto :error_exit
)

if not exist "%PROJECT_ROOT%\.venv\Scripts\activate.bat" (
    echo [错误] 虚拟环境不完整，缺少激活脚本
    echo [提示] 请删除 .venv 目录后重新运行此脚本
    goto :error_exit
)

echo [成功] 虚拟环境检查通过
echo.

:: ========================================
:: 第4步: 设置环境变量
:: ========================================
echo [第4步/6] 设置环境变量...

:: 激活虚拟环境
call "%PROJECT_ROOT%\.venv\Scripts\activate.bat"
if !errorlevel! neq 0 (
    echo [错误] 虚拟环境激活失败
    goto :error_exit
)

:: 设置项目路径到PYTHONPATH
set "PYTHONPATH=%PROJECT_ROOT%;%PROJECT_ROOT%\src;%PYTHONPATH%"

:: 设置工作目录
set "WORKING_DIR=%PROJECT_ROOT%"

:: 优化Python环境变量
set "PYTHONIOENCODING=utf-8"
set "PYTHONUNBUFFERED=1"

:: 设置Qt环境变量（解决Qt平台插件问题）
set "QT_PLUGIN_PATH=%PROJECT_ROOT%\.venv\Lib\site-packages\PyQt5\Qt5\plugins"
set "QT_QPA_PLATFORM_PLUGIN_PATH=%PROJECT_ROOT%\.venv\Lib\site-packages\PyQt5\Qt5\plugins\platforms"
set "PATH=%PROJECT_ROOT%\.venv\Lib\site-packages\PyQt5\Qt5\bin;%PATH%"

:: 设置多进程相关环境变量
for /f %%i in ('wmic cpu get NumberOfCores /value ^| find "="') do set %%i
set "OMP_NUM_THREADS=%NumberOfCores%"
set "MKL_NUM_THREADS=%NumberOfCores%"
set "NUMEXPR_NUM_THREADS=%NumberOfCores%"

echo [成功] 环境变量配置完成
echo   - PYTHONPATH: %PYTHONPATH%
echo   - 工作目录: %WORKING_DIR%
echo   - CPU核心数: %NumberOfCores%
echo   - Qt插件路径: %QT_PLUGIN_PATH%
echo.

:: ========================================
:: 第5步: 检查关键依赖
:: ========================================
echo [第5步/6] 检查关键依赖...

:: 简化的依赖检查
echo [信息] 测试关键模块导入...
"%PYTHON_CMD%" -c "import sys; print('Python版本: ' + sys.version)" 2>nul
if !errorlevel! neq 0 (
    echo [错误] Python基础功能异常
    goto :error_exit
)

:: 检查关键依赖包
set "missing_deps="
"%PYTHON_CMD%" -c "import PyQt5.QtWidgets" 2>nul || set "missing_deps=!missing_deps! PyQt5"
"%PYTHON_CMD%" -c "import pandas" 2>nul || set "missing_deps=!missing_deps! pandas"
"%PYTHON_CMD%" -c "import numpy" 2>nul || set "missing_deps=!missing_deps! numpy"
"%PYTHON_CMD%" -c "import matplotlib" 2>nul || set "missing_deps=!missing_deps! matplotlib"
"%PYTHON_CMD%" -c "import pyecharts" 2>nul || set "missing_deps=!missing_deps! pyecharts"
"%PYTHON_CMD%" -c "import openpyxl" 2>nul || set "missing_deps=!missing_deps! openpyxl"
"%PYTHON_CMD%" -c "import xlrd" 2>nul || set "missing_deps=!missing_deps! xlrd"
"%PYTHON_CMD%" -c "import PIL" 2>nul || set "missing_deps=!missing_deps! Pillow"
"%PYTHON_CMD%" -c "import pyqtgraph" 2>nul || set "missing_deps=!missing_deps! pyqtgraph"

:: 特别检查Qt平台插件
echo [信息] 验证Qt平台插件...
if exist "%QT_PLUGIN_PATH%\platforms\qwindows.dll" (
    echo [成功] Qt Windows平台插件已找到
) else (
    echo [警告] Qt Windows平台插件未找到，GUI可能无法正常显示
)

if not "!missing_deps!"=="" (
    echo [警告] 检测到缺失的依赖包:!missing_deps!
    echo [信息] 正在尝试自动安装缺失依赖...

    :: 检查是否有requirements文件
    set "requirements_file="
    if exist "%PROJECT_ROOT%\requirements.txt" (
        set "requirements_file=%PROJECT_ROOT%\requirements.txt"
    ) else if exist "%PROJECT_ROOT%\temp\tools\requirements.txt" (
        set "requirements_file=%PROJECT_ROOT%\temp\tools\requirements.txt"
    )

    if not "!requirements_file!"=="" (
        echo [信息] 找到依赖配置文件: !requirements_file!
        echo [信息] 正在安装缺失依赖...
        "%PYTHON_CMD%" -m pip install -r "!requirements_file!" --quiet --disable-pip-version-check
        if !errorlevel! equ 0 (
            echo [成功] 依赖安装完成
        ) else (
            echo [警告] 部分依赖安装失败，程序可能无法正常运行
            echo [提示] 请检查网络连接或手动安装依赖
        )
    ) else (
        echo [警告] 未找到依赖配置文件，跳过自动安装
        echo [提示] 如果程序运行异常，请手动安装所需依赖
    )
) else (
    echo [成功] 所有关键依赖检查通过
)
echo.

:: ========================================
:: 第6步: 启动应用程序
:: ========================================
echo [第6步/6] 启动应用程序...

:: 检查run.py文件是否存在
if not exist "%PROJECT_ROOT%\run.py" (
    echo [错误] 找不到应用程序入口文件: run.py
    echo [提示] 请确保在正确的项目目录中运行此脚本
    goto :error_exit
)

:: 显示启动信息
echo [信息] 正在启动智能驾驶试验管控工具...
echo [信息] 入口文件: %PROJECT_ROOT%\run.py
echo [信息] Python解释器: %PYTHON_CMD%
echo [信息] 工作目录: %WORKING_DIR%
echo.

:: 记录启动完成时间
set end_time=%time%
echo 环境配置完成时间: %end_time%
echo.

echo ========================================
echo 正在启动应用程序，请稍候...
echo ========================================
echo.

:: 启动应用程序
cd /d "%WORKING_DIR%"
"%PYTHON_CMD%" "%PROJECT_ROOT%\run.py"

:: 检查应用程序退出状态
set "app_exit_code=%errorlevel%"
echo.
echo ========================================
echo 应用程序已退出
echo ========================================

if !app_exit_code! equ 0 (
    echo [信息] 应用程序正常退出 (退出代码: !app_exit_code!)
) else (
    echo [警告] 应用程序异常退出 (退出代码: !app_exit_code!)
    echo [提示] 如果遇到问题，请查看错误信息或联系技术支持
)

echo.
echo 感谢使用智能驾驶试验管控工具！
echo.
pause
exit /b !app_exit_code!

:: ========================================
:: 错误处理和帮助信息
:: ========================================
:error_exit
echo.
echo ========================================
echo 启动失败 - 错误处理
echo ========================================
echo.
echo [错误] 应用程序启动失败，请根据上述提示解决问题。
echo.
echo [常见解决方案]
echo 1. 环境问题:
echo    - 确保项目文件完整，包含 python\ 目录或 .venv\ 目录
echo    - 检查Python版本是否为3.7或更高版本
echo    - 确保有足够的磁盘空间和内存
echo.
echo 2. 依赖问题:
echo    - 运行以下命令手动安装依赖:
echo      python -m pip install -r requirements.txt
echo      (或者: python -m pip install -r temp\tools\requirements.txt)
echo    - 或者重新创建虚拟环境:
echo      rmdir /s /q .venv
echo      python -m venv .venv
echo      .venv\Scripts\activate.bat
echo      pip install -r requirements.txt
echo.
echo 3. 权限问题:
echo    - 以管理员身份运行此脚本
echo    - 检查防病毒软件是否阻止了程序运行
echo    - 确保项目目录有读写权限
echo.
echo 4. Qt平台插件问题:
echo    - 如果出现 "Could not find the Qt platform plugin windows" 错误
echo    - 运行 test_qt_fix.bat 进行Qt环境诊断
echo    - 确保 .venv\Lib\site-packages\PyQt5\Qt5\plugins 目录完整
echo    - 重新安装PyQt5: pip uninstall PyQt5 ^&^& pip install PyQt5
echo.
echo 5. 系统兼容性:
echo    - 确保Windows版本为Windows 10或更高
echo    - 安装最新的Visual C++ Redistributable
echo    - 检查系统是否缺少必要的运行库
echo.
echo [技术支持]
echo 如果问题仍然存在，请联系技术支持并提供以下信息:
echo - 操作系统版本: %OS%
echo - 错误发生的步骤
echo - 完整的错误信息截图
echo.
echo ========================================
pause
exit /b 1

:: ========================================
:: 脚本结束
:: ========================================