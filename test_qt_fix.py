#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Qt 平台插件测试脚本
用于验证 Qt 环境变量设置是否正确
"""

import sys
import os

def test_qt_environment():
    """测试Qt环境变量"""
    print("=== Qt 环境变量测试 ===")
    
    # 检查环境变量
    qt_plugin_path = os.environ.get('QT_PLUGIN_PATH', '未设置')
    qt_platform_path = os.environ.get('QT_QPA_PLATFORM_PLUGIN_PATH', '未设置')
    
    print(f"QT_PLUGIN_PATH: {qt_plugin_path}")
    print(f"QT_QPA_PLATFORM_PLUGIN_PATH: {qt_platform_path}")
    
    # 检查插件文件是否存在
    if qt_plugin_path != '未设置':
        platforms_path = os.path.join(qt_plugin_path, 'platforms', 'qwindows.dll')
        if os.path.exists(platforms_path):
            print(f"✅ Windows平台插件存在: {platforms_path}")
        else:
            print(f"❌ Windows平台插件不存在: {platforms_path}")
    
    print()

def test_qt_import():
    """测试Qt模块导入"""
    print("=== Qt 模块导入测试 ===")
    
    try:
        from PyQt5.QtCore import QCoreApplication
        print("✅ PyQt5.QtCore 导入成功")
    except ImportError as e:
        print(f"❌ PyQt5.QtCore 导入失败: {e}")
        return False
    
    try:
        from PyQt5.QtWidgets import QApplication
        print("✅ PyQt5.QtWidgets 导入成功")
    except ImportError as e:
        print(f"❌ PyQt5.QtWidgets 导入失败: {e}")
        return False
    
    print()
    return True

def test_qt_application():
    """测试Qt应用程序创建"""
    print("=== Qt 应用程序创建测试 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        
        # 创建应用程序实例
        app = QApplication(sys.argv)
        print("✅ QApplication 创建成功")
        
        # 获取平台信息
        platform_name = app.platformName()
        print(f"✅ 平台名称: {platform_name}")
        
        # 清理
        app.quit()
        del app
        
        return True
        
    except Exception as e:
        print(f"❌ QApplication 创建失败: {e}")
        return False

def main():
    """主函数"""
    print("Qt 平台插件修复验证")
    print("=" * 40)
    print()
    
    # 测试环境变量
    test_qt_environment()
    
    # 测试模块导入
    if not test_qt_import():
        print("❌ Qt模块导入失败，无法继续测试")
        return False
    
    # 测试应用程序创建
    if test_qt_application():
        print("🎉 所有测试通过！Qt环境配置正确。")
        return True
    else:
        print("❌ Qt应用程序创建失败，可能仍存在问题。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
