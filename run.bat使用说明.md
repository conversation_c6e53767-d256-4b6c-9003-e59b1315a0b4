# 智能驾驶试验管控工具 - run.bat 使用说明

## 概述

`run.bat` 是智能驾驶试验管控工具的启动脚本，提供完整的离线环境配置和应用启动功能。

## 功能特性

### 1. 虚拟环境配置
- 自动激活项目根目录下的 `.venv` 虚拟环境
- 如果 `.venv` 不存在，自动创建虚拟环境
- 支持多种 Python 解释器路径配置

### 2. 环境变量设置
- 临时设置必要的 Windows 环境变量（PATH、PYTHONPATH 等）
- 确保虚拟环境的 Python 和 pip 优先级最高
- 设置项目根目录为工作目录
- 优化多进程性能配置

### 3. 启动应用程序
- 使用虚拟环境中的 Python 运行 `run.py` 文件
- 完整的错误处理和状态提示信息
- 中文界面和详细的操作指导

### 4. 路径处理
- 所有路径使用相对路径（相对于 run.bat 文件所在的根目录）
- 确保批处理文件可以从任意位置双击运行
- 自动检测项目根目录

### 5. 离线兼容性
- 支持在没有单独安装 Python 的 Windows 电脑上运行
- 包含必要的依赖检查和自动安装逻辑
- 详细的中文错误提示和解决方案

## 使用方法

### 基本使用
1. 双击 `run.bat` 文件
2. 脚本会自动执行以下步骤：
   - 检查基础环境
   - 配置 Python 解释器路径
   - 配置虚拟环境
   - 设置环境变量
   - 检查关键依赖
   - 启动应用程序

### 支持的 Python 解释器优先级
1. 项目内置 Python：`python\python.exe`
2. 虚拟环境 Python：`.venv\Scripts\python.exe`
3. 系统 Python：系统 PATH 中的 `python`

## 依赖管理

### 自动依赖检查
脚本会自动检查以下关键依赖：
- PyQt5 (GUI框架)
- pandas (数据处理)
- numpy (数值计算)
- matplotlib (图表)
- pyecharts (图表)
- openpyxl (Excel处理)
- xlrd (Excel读取)
- Pillow (图像处理)
- pyqtgraph (实时图表)

### 依赖配置文件
脚本会按以下优先级查找依赖配置：
1. `requirements.txt` (项目根目录)
2. `temp\tools\requirements.txt` (备用位置)

### 自动安装
如果检测到缺失依赖，脚本会尝试自动安装。

## 错误处理

### 常见问题及解决方案

#### 1. 环境问题
- 确保项目文件完整，包含 `python\` 目录或 `.venv\` 目录
- 检查 Python 版本是否为 3.7 或更高版本
- 确保有足够的磁盘空间和内存

#### 2. 依赖问题
手动安装依赖：
```bash
python -m pip install -r requirements.txt
```

重新创建虚拟环境：
```bash
rmdir /s /q .venv
python -m venv .venv
.venv\Scripts\activate.bat
pip install -r requirements.txt
```

#### 3. 权限问题
- 以管理员身份运行脚本
- 检查防病毒软件是否阻止程序运行
- 确保项目目录有读写权限

#### 4. Qt 平台插件问题
如果出现 "Could not find the Qt platform plugin windows" 错误：
```bash
# 运行Qt环境诊断
test_qt_fix.bat

# 重新安装PyQt5
pip uninstall PyQt5
pip install PyQt5
```

#### 5. 系统兼容性
- 确保 Windows 版本为 Windows 10 或更高
- 安装最新的 Visual C++ Redistributable
- 检查系统是否缺少必要的运行库

## 技术特性

### 批处理文件特性
- 使用 UTF-8 编码支持中文显示
- 启用延迟变量扩展
- 完整的错误处理机制
- 详细的执行日志

### 环境配置
- 自动检测 CPU 核心数并优化多进程性能
- 设置 Python 编码为 UTF-8
- 配置无缓冲输出模式
- 自动配置 Qt 环境变量，解决平台插件问题

### 安全性
- 所有路径操作使用引号包围
- 错误状态检查和处理
- 安全的目录切换操作

## 维护说明

### 版本信息
- 当前版本：5.0
- 更新日期：2025-08-04
- 兼容性：Windows 10/11

### 自定义配置
如需修改配置，可以编辑以下部分：
- Python 解释器路径检测逻辑
- 依赖包列表
- 环境变量设置
- 错误提示信息

## 联系支持

如果遇到问题，请提供以下信息：
- 操作系统版本
- 错误发生的步骤
- 完整的错误信息截图
- Python 版本信息
